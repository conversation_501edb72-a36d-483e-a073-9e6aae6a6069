# Gladius Sales Hierarchy v2 Migration - Overview Document

| Document Type | Technical Overview & Implementation Guide |
| :---- | :---- |
| Created By | [<EMAIL>](https://frontpage.phonepe.com/people/dharmik.popat) |
| Date | 30 Oct 2025 |
| Related RFC | Gladius_Sales_Hierarchy_v2_Migration_RFC.md |

## Executive Summary

This document provides a high-level overview of the Gladius Sales Hierarchy v2 migration, which transitions from Role+BU based hierarchy to Organization-level constructs. The migration involves three key components:

1. **Core Hierarchy Migration**: Update task filtering from Role+BU to OrgId+RoleV2
2. **Tags Implementation**: Solve OR/AND logic issues with hierarchical tag structure
3. **Rules Engine** (Long-term): Replace Chimera with TaskDefinition-level visibility rules

## Current vs New Hierarchy

### Data Structure Changes
```java
// Current AgentProfile
agentType: "AGENT"
businessUnit: "MASS_RETAIL"
attributes: [tags...]

// New AgentProfile (Hierarchy v2)
roleV2: "FIELD_AGENT"
orgId: "ORG_MR_LENDING_DIRECT"
businessUnitV2: "MASS_RETAIL"
channel: "DIRECT"
businessType: "LENDING"
```

### Hierarchy Mapping
```
Old: Role=AGENT + BU=MASS_RETAIL
New: RoleV2=FIELD_AGENT + OrgId=ORG_MR_LENDING_DIRECT
```

## Core Implementation Changes

### 1. Task Filtering Logic Updates

**Files to Modify:**
- `BaseSearchRequestQueryBuilder.java` - Add hierarchy v2 filtering
- `TaskEsUtils.java` - Add org-based filter methods
- `LegionService.java` - Handle new AgentProfile fields

**Key Changes:**
```java
// New filtering method
private BoolQueryBuilder getHierarchyFilter(AgentProfile userProfile) {
    BoolQueryBuilder hierarchyQuery = new BoolQueryBuilder();
    
    if (hasHierarchyV2(userProfile)) {
        hierarchyQuery.should(getOrgBasedFilter(userProfile));
    }
    
    // Backward compatibility
    hierarchyQuery.should(getLegacyHierarchyFilter(userProfile));
    
    return hierarchyQuery;
}
```

### 2. Elasticsearch Schema Updates

**New Fields Added:**
```json
{
  "orgId": {"type": "keyword"},
  "roleV2": {"type": "keyword"},
  "businessUnitV2": {"type": "keyword"},
  "channel": {"type": "keyword"},
  "businessType": {"type": "keyword"},
  "hierarchyVersion": {"type": "keyword"}
}
```

### 3. Campaign Creation Enhancements

**New Request Structure:**
```json
{
  "name": "Campaign Name",
  "hierarchyV2": {
    "enabled": true,
    "orgIds": ["ORG_MR_LENDING_DIRECT"],
    "roleV2Combinations": [{
      "roles": ["FIELD_AGENT"],
      "businessUnits": ["MASS_RETAIL"],
      "operator": "AND"
    }]
  }
}
```

## Tags Implementation Solution

### Problem Statement
Current tags use OR-based matching in Elasticsearch, but new hierarchy requires AND logic for Role+BU combinations.

### Solution: Hierarchical Underscore-Delimited Tags

**Tag Structure Pattern:**
```
{orgId}_{roleV2}_{cohort?}
```

**Examples:**
```java
// Agent tags
"ORG_MR_LENDING_DIRECT_FIELD_AGENT"
"ORG_MR_LENDING_DIRECT_FIELD_AGENT_EXCLUSIVE_LENDING"

// Task tags (with ANY wildcards)
"ORG_MR_LENDING_DIRECT_ANY"                    // Any role in org
"ORG_MR_LENDING_DIRECT_ANY_EXCLUSIVE_LENDING"  // Any role + cohort
"ANY_ANY_HIGH_VALUE"                           // Cross-org cohort
```

**Benefits:**
- ✅ No Legion schema changes required
- ✅ Simple ES terms query (no complex boolean logic)
- ✅ Flexible matching with ANY wildcards
- ✅ Eliminates need for Chimera restrictions
- ✅ Self-contained hierarchy information

### Implementation
```java
public static Set<String> hierarchicalTagEnricher(AgentProfile agentProfile) {
    Set<String> enrichedTags = new HashSet<>();
    String orgId = agentProfile.getOrgId();
    String roleV2 = agentProfile.getRoleV2();
    
    if (orgId != null && roleV2 != null) {
        enrichedTags.add(orgId + "_" + roleV2);
        
        // Add cohort-specific combinations
        for (String cohort : getCohortTags(agentProfile)) {
            enrichedTags.add(orgId + "_" + roleV2 + "_" + cohort);
        }
    }
    
    return enrichedTags;
}
```

## Long-Term Solution: TaskDefinition Rules Engine

### Vision
Replace Chimera with embedded visibility rules in TaskDefinition, providing:
- Granular definition-level control
- No external service dependencies
- Cached performance (500-700 definitions)
- Flexible rule management APIs

### Enhanced TaskDefinition Model
```java
public class StoredTaskDefinition {
    private String definitionId;
    private String actionId;
    
    // NEW: Embedded rules engine
    private TaskVisibilityRules visibilityRules;
    
    // Current fields...
}

public class TaskVisibilityRules {
    private boolean rulesEnabled;
    private List<VisibilityCondition> inclusionRules;  // MUST match
    private List<VisibilityCondition> exclusionRules;  // MUST NOT match
    private String ruleLogic; // "AND" or "OR"
}
```

### Rule Examples
```json
{
  "visibilityRules": {
    "rulesEnabled": true,
    "inclusionRules": [{
      "conditionType": "CONTAINS_ANY",
      "field": "agentTags",
      "values": ["EDC_SERVICING"],
      "operator": "OR"
    }],
    "exclusionRules": [{
      "conditionType": "CONTAINS_ANY", 
      "field": "agentTags",
      "values": ["RESTRICTED_LENDING"],
      "operator": "OR"
    }]
  }
}
```

### Performance Strategy
```java
// Cached evaluation
@Cacheable("visibleDefinitions")
public Set<String> getVisibleDefinitionIds(String agentId) {
    AgentProfile agent = legionService.getAgentProfile(agentId);
    List<StoredTaskDefinition> allDefinitions = getAllActiveDefinitions(); // Cached
    
    return allDefinitions.stream()
        .filter(def -> rulesEngine.isTaskVisibleToAgent(def, agent))
        .map(StoredTaskDefinition::getDefinitionId)
        .collect(Collectors.toSet());
}
```

## Implementation Phases

### Phase 1: Core Hierarchy Migration (3-4 weeks)
1. Update filtering logic in BaseSearchRequestQueryBuilder
2. Add new ES fields and update mappings
3. Implement backward compatibility
4. Update campaign creation APIs

### Phase 2: Tags Implementation (2-3 weeks)
1. Implement hierarchical tag enricher
2. Update campaign creation with new tag structure
3. Migrate existing campaigns
4. Test tag matching scenarios

### Phase 3: Rules Engine (Long-term, 6 weeks)
1. Build TaskVisibilityRules model and evaluation engine
2. Create management APIs and caching layer
3. Build Chimera migration tooling
4. Gradual migration and Chimera removal

## Key Benefits

### Immediate Benefits (Phase 1-2)
- ✅ Support for new Sales Hierarchy v2
- ✅ Proper AND logic for Role+BU combinations
- ✅ Backward compatibility during transition
- ✅ Cleaner tag-based filtering

### Long-term Benefits (Phase 3)
- ✅ Eliminate Chimera dependency
- ✅ Granular definition-level control
- ✅ Improved performance with caching
- ✅ Self-contained visibility logic
- ✅ Flexible rule management

## Risk Mitigation

### Technical Risks
- **Task Visibility Issues**: Extensive testing, gradual rollout, monitoring
- **Performance Impact**: Query optimization, caching, load testing
- **Migration Complexity**: Phased approach, parallel evaluation, rollback plans

### Operational Risks
- **Data Inconsistency**: Validation scripts, reconciliation tools
- **Backward Compatibility**: Feature flags, dual processing
- **Cache Management**: Proper invalidation, warming strategies

## Success Criteria

1. **Functional**: All existing task visibility rules work with new hierarchy
2. **Performance**: Task discovery < 500ms (95th percentile)
3. **Reliability**: Zero data loss, 99.9% task visibility accuracy
4. **Maintainability**: Reduced complexity, eliminated external dependencies

---

*For detailed technical implementation, API contracts, and code examples, refer to the complete RFC document: `Gladius_Sales_Hierarchy_v2_Migration_RFC.md`*
