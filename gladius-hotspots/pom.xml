<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gladius</artifactId>
        <groupId>com.phonepe.merchant</groupId>
        <version>1.0.383-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>gladius-hotspots</artifactId>

    <properties>
        <maven.deploy.skip>false</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>gladius-models</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <version>${junit.version}</version>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
