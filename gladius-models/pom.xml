<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gladius</artifactId>
        <groupId>com.phonepe.merchant</groupId>
        <version>1.0.384-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>gladius-models</artifactId>

    <properties>
        <maven.deploy.skip>false</maven.deploy.skip>
    </properties>

    <dependencies>
        <!-- https://mvnrepository.com/artifact/javax.validation/validation-api -->
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.1</version>
        </dependency>
        <dependency>
            <groupId>io.appform.dropwizard.sharding</groupId>
            <artifactId>db-sharding-bundle</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <version>${junit.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-envers</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.phonepe.models</groupId>
            <artifactId>merchant-service-model</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.fsm</groupId>
            <artifactId>fsm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.services</groupId>
            <artifactId>discovery-models</artifactId>
            <version>1.1.42</version>
            <exclusions>
                <exclusion>
                    <artifactId>feign-ranger</artifactId>
                    <groupId>feign.ranger</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.phonepe.platform</groupId>
                    <artifactId>dropwizard-requestinfo-bundle</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jersey.core</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>dropwizard-requestinfo-bundle</groupId>
                    <artifactId>com.phonepe.platform</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.phonepe.models</groupId>
                    <artifactId>phonepe-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.phonepe.platform.http</groupId>
                    <artifactId>http-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>brickbat-models</artifactId>
        </dependency>
        <dependency>
            <groupId>com.flipkart.foxtrot</groupId>
            <artifactId>foxtrot-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.appform.hbase.ds</groupId>
                    <artifactId>hbase-ds</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hbase</groupId>
                    <artifactId>hbase-shaded-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.foxtrot</groupId>
                    <artifactId>foxtrot-common</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>ranger</artifactId>
                    <groupId>com.flipkart.ranger</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>feign-ranger</artifactId>
                    <groupId>feign.ranger</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.phonepe.platform</groupId>
                    <artifactId>event-ingestion-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.phonepe.platform.http.v2</groupId>
                    <artifactId>http-feign</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.phonepe.dataplatform</groupId>
            <artifactId>event-ingestion-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>ranger</artifactId>
                    <groupId>com.flipkart.ranger</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>feign-ranger</artifactId>
                    <groupId>feign.ranger</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>legion-models</artifactId>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchants</groupId>
            <artifactId>odin-ace-models</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.phonepe.merchant</groupId>
                    <artifactId>phonepe-models</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.hypersistence</groupId>
            <artifactId>hypersistence-utils-hibernate-55</artifactId>
            <version>3.8.2</version>
        </dependency>
    </dependencies>
</project>