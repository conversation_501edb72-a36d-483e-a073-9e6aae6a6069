| Created | By [<EMAIL>](https://frontpage.phonepe.com/people/dharmik.popat) at 20 Oct 2025 |
| :---- | :---- |
| RFC | TBD |
| Type | Engineering requirements document for Gladius Sales Hierarchy v2 Migration |

## Table of Contents

[**Summary	1**](#summary)

[**Current vs New Hierarchy	1**](#current-vs-new-hierarchy)

[**Elasticsearch Schema Changes	2**](#elasticsearch-schema-changes)

[**Task Filtering Logic Changes	2**](#task-filtering-logic-changes)

[**API Contract Changes	3**](#api-contract-changes)

[**Campaign Creation Changes	4**](#campaign-creation-changes)

[**Task Action/Definition Changes	5**](#task-actiondefinition-changes)

[**Implementation Details	5**](#implementation-details)

---

## Summary

Gladius needs to support Sales Hierarchy v2 where every combination of Business Unit (BU) + Business Type (BT) + Channel becomes an organization. This requires:

1. **Task Visibility**: Change from Role+BU filtering to OrgId+RoleV2 filtering
2. **Campaign Creation**: Support AND-based Role+BU combinations with inclusion/exclusion logic
3. **Elasticsearch**: Add new hierarchy fields for filtering
4. **API Changes**: Update task creation, campaign APIs to support new hierarchy

---

## Current vs New Hierarchy

### Current Hierarchy Fields (AgentProfile)
```java
private AgentType agentType;           // Role (AGENT, MANAGER, etc.)
private BusinessUnit businessUnit;     // BU (MASS_RETAIL, SME, etc.)
private List<UserBaseAttribute> attributes; // Tags
```

### New Hierarchy Fields (AgentProfile)
```java
private String roleV2;                 // New role field
private String orgId;                  // Organization ID (BU+BT+Channel)
private String businessUnitV2;         // New BU field
private String channel;                // DIRECT, PARTNER, etc.
private String businessType;           // LENDING, PAYMENTS, etc.
```

### Hierarchy Mapping Example
```
Old: Role=AGENT, BU=MASS_RETAIL
New: RoleV2=FIELD_AGENT, OrgId=ORG_MR_LENDING_DIRECT, BU=MASS_RETAIL, Channel=DIRECT, BT=LENDING
```

---

## Elasticsearch Schema Changes

### Current Task Index Mapping
```json
{
  "mappings": {
    "properties": {
      "rolesNotAllowed": {
        "type": "keyword"
      },
      "tags": {
        "type": "keyword"
      },
      "businessUnit": {
        "type": "keyword"
      }
    }
  }
}
```

### New Task Index Mapping (Additional Fields)
```json
{
  "mappings": {
    "properties": {
      "orgId": {
        "type": "keyword"
      },
      "roleV2": {
        "type": "keyword"
      },
      "businessUnitV2": {
        "type": "keyword"
      },
      "channel": {
        "type": "keyword"
      },
      "businessType": {
        "type": "keyword"
      },
      "hierarchyVersion": {
        "type": "keyword"
      }
    }
  }
}
```

### Task Document Structure Changes
```json
{
  "taskInstanceId": "TASK_001",
  "actionId": "ACTION_001",
  "entityId": "MERCHANT_001",

  // Current hierarchy fields (maintain for backward compatibility)
  "rolesNotAllowed": ["MANAGER"],
  "tags": ["LENDING", "HIGH_VALUE"],
  "businessUnit": "MASS_RETAIL",

  // New hierarchy fields
  "orgId": "ORG_MR_LENDING_DIRECT",
  "roleV2": "FIELD_AGENT",
  "businessUnitV2": "MASS_RETAIL",
  "channel": "DIRECT",
  "businessType": "LENDING",
  "hierarchyVersion": "v2"
}
```

---

## Task Filtering Logic Changes

### Current Filtering Implementation
```java
// BaseSearchRequestQueryBuilder.java
public BoolQueryBuilder getQuery(T request, String actor) {
    AgentProfile userProfile = legionService.getAgentProfile(actor);
    BoolQueryBuilder query = getBaseQuery(states);

    // Current filtering
    query.must(getTagFilter(ProfileUtils.tagEnricher(userProfile)));
    query.must(TaskEsUtils.getRoleFilter(userProfile.getAgentType().toString()));

    return query;
}
```

### New Filtering Implementation
```java
// Updated BaseSearchRequestQueryBuilder.java
public BoolQueryBuilder getQuery(T request, String actor) {
    AgentProfile userProfile = legionService.getAgentProfile(actor);
    BoolQueryBuilder query = getBaseQuery(states);

    // New hierarchy-aware filtering
    query.must(getTagFilter(ProfileUtils.tagEnricher(userProfile)));
    query.must(getHierarchyFilter(userProfile));

    return query;
}

private BoolQueryBuilder getHierarchyFilter(AgentProfile userProfile) {
    BoolQueryBuilder hierarchyQuery = new BoolQueryBuilder();

    // Check if agent has new hierarchy fields
    if (hasHierarchyV2(userProfile)) {
        // Use new hierarchy filtering
        hierarchyQuery.should(getOrgBasedFilter(userProfile));
    }

    // Always include old hierarchy for backward compatibility
    hierarchyQuery.should(getLegacyHierarchyFilter(userProfile));

    return hierarchyQuery;
}

private BoolQueryBuilder getOrgBasedFilter(AgentProfile userProfile) {
    BoolQueryBuilder orgQuery = new BoolQueryBuilder();

    // Task must be visible to agent's organization
    orgQuery.must(QueryBuilders.termQuery("orgId", userProfile.getOrgId()));

    // Task must be allowed for agent's role
    orgQuery.mustNot(QueryBuilders.termQuery("rolesNotAllowed", userProfile.getRoleV2()));

    return orgQuery;
}
```

### New TaskEsUtils Methods
```java
// TaskEsUtils.java - New methods
public static BoolQueryBuilder getOrgFilter(String orgId) {
    return QueryBuilders.boolQuery()
        .must(QueryBuilders.termQuery("orgId", orgId));
}

public static BoolQueryBuilder getRoleV2Filter(String roleV2) {
    return QueryBuilders.boolQuery()
        .mustNot(QueryBuilders.termQuery("rolesNotAllowed", roleV2));
}

public static BoolQueryBuilder getHierarchyVersionFilter(String version) {
    return QueryBuilders.boolQuery()
        .must(QueryBuilders.termQuery("hierarchyVersion", version));
}
```

---

## API Contract Changes

### Task Discovery APIs (No Contract Changes)
All existing task discovery APIs maintain the same request/response contracts. Changes are internal to filtering logic only.

**Affected Endpoints:**
- `GET /v1/task/search/discovery`
- `GET /v1/task/search/assigned`
- `GET /v1/task/search/escalated`
- `POST /v1/task/search` (with filters)

**Internal Changes:**
- Updated `BaseSearchRequestQueryBuilder` to use new hierarchy fields
- Enhanced `TaskEsUtils` with org-based filtering methods
- Backward compatibility maintained for agents without hierarchy v2

### Campaign Creation API Changes

#### Current Campaign Creation
**Endpoint**: `POST /v1/campaign/create`

**Current Request:**
```json
{
  "name": "Campaign Name",
  "description": "Campaign Description",
  "tags": ["TAG1", "TAG2"],
  "businessUnits": ["MASS_RETAIL", "SME"],
  "roles": ["AGENT", "MANAGER"],
  "filterOperator": "OR"
}
```

#### Enhanced Campaign Creation (New)
**Endpoint**: `POST /v1/campaign/create`

**New Request Structure:**
```json
{
  "name": "Campaign Name",
  "description": "Campaign Description",

  // Legacy fields (maintain backward compatibility)
  "tags": ["TAG1", "TAG2"],
  "businessUnits": ["MASS_RETAIL"],
  "roles": ["AGENT"],
  "filterOperator": "OR",

  // New hierarchy v2 fields
  "hierarchyV2": {
    "enabled": true,
    "orgIds": ["ORG_MR_LENDING_DIRECT", "ORG_SME_PAYMENTS_PARTNER"],
    "roleV2Combinations": [
      {
        "roles": ["FIELD_AGENT", "SENIOR_AGENT"],
        "businessUnits": ["MASS_RETAIL"],
        "channels": ["DIRECT"],
        "businessTypes": ["LENDING"],
        "operator": "AND"
      }
    ],
    "inclusionExclusionRules": {
      "include": ["R,B", "O,C"],
      "exclude": ["R,O"]
    },
    "tagOperator": "AND"
  }
}
```

**Response:** No changes to response structure

### Task Action/Definition Creation API Changes

#### Task Action Creation
**Endpoint**: `POST /v1/task/action/create`

**Enhanced Request:**
```json
{
  "actionId": "ACTION_001",
  "name": "Action Name",
  "description": "Action Description",

  // Current hierarchy support
  "rolesNotAllowed": ["MANAGER"],
  "businessUnits": ["MASS_RETAIL"],

  // New hierarchy v2 support
  "hierarchyV2": {
    "orgIds": ["ORG_MR_LENDING_DIRECT"],
    "roleV2NotAllowed": ["SENIOR_MANAGER"],
    "businessUnitV2": ["MASS_RETAIL"],
    "channels": ["DIRECT"],
    "businessTypes": ["LENDING"]
  },

  "entityType": "MERCHANT",
  "verificationStrategy": {...},
  "completionValidationStrategy": {...}
}
```

#### Task Definition Creation
**Endpoint**: `POST /v1/task/definition/create`

**Enhanced Request:**
```json
{
  "definitionId": "DEF_001",
  "actionId": "ACTION_001",
  "name": "Definition Name",

  // New hierarchy v2 fields
  "hierarchyV2": {
    "orgId": "ORG_MR_LENDING_DIRECT",
    "applicableRoleV2": ["FIELD_AGENT"],
    "businessUnitV2": "MASS_RETAIL",
    "channel": "DIRECT",
    "businessType": "LENDING"
  },

  "attributes": {...},
  "definitionAttributes": {...}
}
```

---

## Campaign Creation Changes

### Current Campaign Logic
```java
// CampaignServiceImpl.java - Current implementation
public Campaign save(Campaign request) {
    validate(request);

    // Current: OR-based filtering for tags and roles
    List<String> tags = request.getTags();
    List<String> roles = request.getRoles();
    List<String> businessUnits = request.getBusinessUnits();

    // Creates tasks visible to ANY agent matching ANY of the criteria
    StoredCampaign save = campaignRepository.save(CampaignTransformationUtils.toStoredCampaign(request));
    return CampaignTransformationUtils.toCampaign(save);
}
```

### New Campaign Logic (Hierarchy v2)
```java
// Enhanced CampaignServiceImpl.java
public Campaign save(Campaign request) {
    validate(request);

    if (request.getHierarchyV2() != null && request.getHierarchyV2().isEnabled()) {
        // New: AND-based filtering with org support
        return saveWithHierarchyV2(request);
    } else {
        // Fallback to legacy campaign creation
        return saveLegacyCampaign(request);
    }
}

private Campaign saveWithHierarchyV2(Campaign request) {
    HierarchyV2Config config = request.getHierarchyV2();

    // Create tasks for specific org combinations
    List<TaskDefinition> taskDefinitions = new ArrayList<>();

    for (String orgId : config.getOrgIds()) {
        for (RoleV2Combination combination : config.getRoleV2Combinations()) {
            if (matchesInclusionExclusionRules(combination, config.getInclusionExclusionRules())) {
                TaskDefinition taskDef = createTaskDefinitionForOrg(orgId, combination, request);
                taskDefinitions.add(taskDef);
            }
        }
    }

    // Save campaign with org-specific task definitions
    StoredCampaign save = campaignRepository.save(CampaignTransformationUtils.toStoredCampaign(request));
    return CampaignTransformationUtils.toCampaign(save);
}
```

### Inclusion/Exclusion Rules Logic
```java
// New method for handling R,B/O,C logic
private boolean matchesInclusionExclusionRules(RoleV2Combination combination, InclusionExclusionRules rules) {
    // Include rules: ["R,B", "O,C"] means include Role+BU OR Org+Channel combinations
    for (String includeRule : rules.getInclude()) {
        if (matchesRule(combination, includeRule)) {
            // Check exclude rules
            for (String excludeRule : rules.getExclude()) {
                if (matchesRule(combination, excludeRule)) {
                    return false; // Excluded
                }
            }
            return true; // Included and not excluded
        }
    }
    return false; // Not included
}

private boolean matchesRule(RoleV2Combination combination, String rule) {
    String[] parts = rule.split(",");
    // R,B = Role + BusinessUnit
    // O,C = Org + Channel
    // R,O = Role + Org
    // etc.

    switch (rule) {
        case "R,B":
            return combination.getRoles() != null && combination.getBusinessUnits() != null;
        case "O,C":
            return combination.getOrgId() != null && combination.getChannels() != null;
        case "R,O":
            return combination.getRoles() != null && combination.getOrgId() != null;
        default:
            return false;
    }
}
```

---

## Task Action/Definition Changes

### Current Task Action Model
```java
// Current StoredTaskAction
public class StoredTaskAction {
    private String actionId;
    private String name;
    private List<String> rolesNotAllowed;
    private List<String> businessUnits;
    private Map<String, Set<String>> attributes;
    // ... other fields
}
```

### Enhanced Task Action Model
```java
// Enhanced StoredTaskAction with hierarchy v2 support
public class StoredTaskAction {
    private String actionId;
    private String name;

    // Legacy hierarchy fields (maintain backward compatibility)
    private List<String> rolesNotAllowed;
    private List<String> businessUnits;

    // New hierarchy v2 fields
    private HierarchyV2Config hierarchyV2;
    private String hierarchyVersion; // "v1" or "v2"

    private Map<String, Set<String>> attributes;
    // ... other fields
}

// New hierarchy v2 configuration
public class HierarchyV2Config {
    private List<String> orgIds;
    private List<String> roleV2NotAllowed;
    private List<String> businessUnitV2;
    private List<String> channels;
    private List<String> businessTypes;
}
```

### Task Definition Changes
```java
// Enhanced TaskDefinitionServiceImpl
public TaskDefinitionInstance save(TaskDefinitionCreateRequest request) {
    validateTaskDefinitionId(request);

    TaskActionInstance taskActionInstance = taskActionService.getFromDB(
        TaskActionFetchByIdRequest.builder()
            .taskActionId(request.getActionId())
            .build()
    );

    // Enhanced validation for hierarchy v2
    if (request.getHierarchyV2() != null) {
        validateHierarchyV2Compatibility(request.getHierarchyV2(), taskActionInstance);
    }

    TaskDefinitionInstance instance = TaskDefinitionTransformationUtils.toTaskDefinitionInstance(request, taskActionInstance);

    // Set hierarchy version
    instance.setHierarchyVersion(request.getHierarchyV2() != null ? "v2" : "v1");

    StoredTaskDefinition storedTaskDefinition = TaskDefinitionTransformationUtils.toStoredTask(instance);
    StoredTaskDefinition savedStoredTaskDefinition = taskDefinitionRepository.save(storedTaskDefinition);

    return TaskDefinitionTransformationUtils.toTaskDefinitionInstance(savedStoredTaskDefinition);
}

private void validateHierarchyV2Compatibility(HierarchyV2Config hierarchyV2, TaskActionInstance taskAction) {
    // Validate that task action supports the specified org/role combinations
    if (taskAction.getHierarchyV2() != null) {
        // Ensure definition's org is compatible with action's allowed orgs
        if (!taskAction.getHierarchyV2().getOrgIds().contains(hierarchyV2.getOrgId())) {
            throw new ValidationException("Task definition org not compatible with task action");
        }
    }
}
```

---

## Implementation Details

### Key Files to Modify

#### 1. Core Filtering Logic
- **File**: `gladius-tasks/src/main/java/com/phonepe/merchant/legion/tasks/search/query/BaseSearchRequestQueryBuilder.java`
- **Changes**: Add `getHierarchyFilter()` method, update `getQuery()` method

- **File**: `gladius-tasks/src/main/java/com/phonepe/merchant/legion/tasks/utils/TaskEsUtils.java`
- **Changes**: Add `getOrgFilter()`, `getRoleV2Filter()`, `getHierarchyVersionFilter()` methods

#### 2. Campaign Management
- **File**: `gladius-tasks/src/main/java/com/phonepe/merchant/legion/tasks/services/impl/CampaignServiceImpl.java`
- **Changes**: Add hierarchy v2 support, inclusion/exclusion logic

- **File**: `gladius-models/src/main/java/com/phonepe/merchant/gladius/models/campaign/Campaign.java`
- **Changes**: Add `HierarchyV2Config` field

#### 3. Task Action/Definition
- **File**: `gladius-tasks/src/main/java/com/phonepe/merchant/legion/tasks/services/impl/TaskActionServiceImpl.java`
- **Changes**: Add hierarchy v2 validation and support

- **File**: `gladius-tasks/src/main/java/com/phonepe/merchant/legion/tasks/services/impl/TaskDefinitionServiceImpl.java`
- **Changes**: Add hierarchy v2 compatibility validation

#### 4. Agent Profile Integration
- **File**: `gladius-core/src/main/java/com/phonepe/merchant/legion/core/services/LegionService.java`
- **Changes**: Handle new hierarchy fields in agent profile responses

### Elasticsearch Index Updates
- **Index**: `agent_tasks_v6` (or create new version `agent_tasks_v7`)
- **New Fields**: `orgId`, `roleV2`, `businessUnitV2`, `channel`, `businessType`, `hierarchyVersion`
- **Migration**: Reindex existing tasks with `hierarchyVersion: "v1"`

### Feature Flag Implementation
```java
// Add feature flag for gradual rollout
@Component
public class HierarchyV2FeatureFlag {

    @Value("${gladius.hierarchy.v2.enabled:false}")
    private boolean hierarchyV2Enabled;

    @Value("${gladius.hierarchy.v2.rollout.percentage:0}")
    private int rolloutPercentage;

    public boolean isHierarchyV2Enabled(String agentId) {
        if (!hierarchyV2Enabled) {
            return false;
        }

        // Gradual rollout based on agent ID hash
        int hash = Math.abs(agentId.hashCode() % 100);
        return hash < rolloutPercentage;
    }
}
```

### Backward Compatibility Strategy
1. **Dual Filtering**: Support both old and new hierarchy in parallel
2. **Graceful Degradation**: Fall back to old hierarchy if new fields are missing
3. **Feature Flags**: Control rollout percentage and enable/disable new hierarchy
4. **Data Migration**: Gradual migration of agents from old to new hierarchy
5. **Monitoring**: Track task visibility accuracy during transition

---

## Tags Implementation Approaches

### Current Tags Problem
The existing tags implementation has a fundamental issue with OR-based matching in Elasticsearch:

**Current Flow:**
1. Campaign created with tags for BU + Role (separate rows in `gladius.tags` table)
2. TaskInstance gets tags from campaign → ES document: `tags: ["MASS_RETAIL", "AGENT"]`
3. Agent profile has tags from Legion → `tags: ["EXCLUSIVE_LENDING", "MASS_RETAIL", "AGENT"]`
4. ES query: `should(tags.contains("MASS_RETAIL") OR tags.contains("AGENT"))` → **OR behavior**
5. Chimera restrictions used to block other roles/BUs

**Problem**: Need AND logic for new hierarchy: `tags.contains("MASS_RETAIL") AND tags.contains("AGENT")`

### Approach 1: New Legion User Attribute Types

#### Implementation
```java
// Legion user_attributes table - New attribute types
type = "TAG_BU" → value = "MASS_RETAIL"
type = "TAG_ROLE" → value = "AGENT"
type = "TAG_CHANNEL" → value = "DIRECT"
type = "TAG_BUSINESS_TYPE" → value = "LENDING"
type = "TAG" → value = "EXCLUSIVE_LENDING" // Generic cohort tags
```

#### Enhanced ProfileUtils
```java
public static EnrichedTags tagEnricher(AgentProfile agentProfile) {
    Set<String> buTags = new HashSet<>();
    Set<String> roleTags = new HashSet<>();
    Set<String> channelTags = new HashSet<>();
    Set<String> businessTypeTags = new HashSet<>();
    Set<String> cohortTags = new HashSet<>();

    agentProfile.getAttributes().stream()
        .filter(UserBaseAttribute::isActive)
        .forEach(attr -> {
            switch (attr.getType()) {
                case TAG_BU: buTags.add(((TagsAttribute) attr).getValue()); break;
                case TAG_ROLE: roleTags.add(((TagsAttribute) attr).getValue()); break;
                case TAG_CHANNEL: channelTags.add(((TagsAttribute) attr).getValue()); break;
                case TAG_BUSINESS_TYPE: businessTypeTags.add(((TagsAttribute) attr).getValue()); break;
                case TAG: cohortTags.add(((TagsAttribute) attr).getValue()); break;
            }
        });

    return EnrichedTags.builder()
        .buTags(buTags).roleTags(roleTags).channelTags(channelTags)
        .businessTypeTags(businessTypeTags).cohortTags(cohortTags).build();
}
```

#### Elasticsearch Document Structure
```json
{
  "taskInstanceId": "TASK_001",
  "tags": {
    "bu": ["MASS_RETAIL"],
    "role": ["AGENT", "SENIOR_AGENT"],
    "channel": ["DIRECT"],
    "businessType": ["LENDING"],
    "cohort": ["EXCLUSIVE_LENDING", "HIGH_VALUE"]
  }
}
```

#### Enhanced Filtering Logic
```java
public static BoolQueryBuilder getEnrichedTagFilter(EnrichedTags agentTags) {
    BoolQueryBuilder tagQuery = new BoolQueryBuilder();

    // AND logic for hierarchy tags
    if (!agentTags.getBuTags().isEmpty()) {
        tagQuery.must(QueryBuilders.termsQuery("tags.bu", agentTags.getBuTags()));
    }
    if (!agentTags.getRoleTags().isEmpty()) {
        tagQuery.must(QueryBuilders.termsQuery("tags.role", agentTags.getRoleTags()));
    }
    if (!agentTags.getChannelTags().isEmpty()) {
        tagQuery.must(QueryBuilders.termsQuery("tags.channel", agentTags.getChannelTags()));
    }
    if (!agentTags.getBusinessTypeTags().isEmpty()) {
        tagQuery.must(QueryBuilders.termsQuery("tags.businessType", agentTags.getBusinessTypeTags()));
    }

    // OR logic for cohort tags (existing behavior)
    if (!agentTags.getCohortTags().isEmpty()) {
        tagQuery.should(QueryBuilders.termsQuery("tags.cohort", agentTags.getCohortTags()));
    }

    return tagQuery;
}
```

**Pros:**
- Clean separation of tag types
- Easy to query specific attribute types
- Backward compatible with existing `type=TAG`

**Cons:**
- Requires Legion schema/API changes
- Multiple attribute types to manage

---

### Approach 2: Hierarchical Underscore-Delimited Tags (Recommended)

#### Tag Structure Pattern
```
{orgId}_{roleV2}_{cohort?}
```

#### Implementation Examples
```java
// Legion user_attributes table - type = "TAG"
value = "ORG_MR_LENDING_DIRECT_FIELD_AGENT"           // Specific org + role
value = "ORG_MR_LENDING_DIRECT_ANY"                   // Any role in this org
value = "ORG_MR_LENDING_DIRECT_FIELD_AGENT_EXCLUSIVE_LENDING"  // Org + role + cohort
value = "ORG_MR_LENDING_DIRECT_ANY_EXCLUSIVE_LENDING" // Any role in org + cohort
value = "ANY_ANY_HIGH_VALUE"                          // Any org, any role, specific cohort
```

#### Enhanced ProfileUtils for Hierarchical Tags
```java
public static Set<String> hierarchicalTagEnricher(AgentProfile agentProfile) {
    Set<String> enrichedTags = new HashSet<>();

    String orgId = agentProfile.getOrgId();
    String roleV2 = agentProfile.getRoleV2();

    // Get cohort tags from existing attributes
    Set<String> cohortTags = agentProfile.getAttributes().stream()
        .filter(attr -> attr.getType() == ProfileAttributeType.TAG)
        .filter(UserBaseAttribute::isActive)
        .map(attr -> ((TagsAttribute) attr).getValue())
        .filter(tag -> !tag.startsWith("ORG_")) // Exclude hierarchy tags
        .collect(Collectors.toSet());

    // Generate base hierarchy tag
    if (orgId != null && roleV2 != null) {
        enrichedTags.add(orgId + "_" + roleV2);

        // Generate cohort-specific tags
        for (String cohort : cohortTags) {
            enrichedTags.add(orgId + "_" + roleV2 + "_" + cohort);
        }
    }

    // Add original cohort tags for backward compatibility
    enrichedTags.addAll(cohortTags);

    return enrichedTags;
}
```

#### Elasticsearch Document Structure
```json
{
  "taskInstanceId": "TASK_001",
  "tags": [
    "ORG_MR_LENDING_DIRECT_ANY",           // Any role in this org can take
    "ORG_MR_LENDING_DIRECT_ANY_EXCLUSIVE_LENDING"  // + specific cohort
  ]
}
```

#### Campaign Creation with Hierarchical Tags
```json
{
  "name": "Lending Campaign for Mass Retail",
  "hierarchicalTags": [
    "ORG_MR_LENDING_DIRECT_ANY",                    // Any role in this org
    "ORG_SME_LENDING_PARTNER_SENIOR_AGENT",        // Specific role in different org
    "ORG_MR_LENDING_DIRECT_ANY_EXCLUSIVE_LENDING"  // Any role + specific cohort
  ]
}
```

#### Simple Filtering Logic
```java
public static BoolQueryBuilder getHierarchicalTagFilter(Set<String> agentTags) {
    BoolQueryBuilder tagQuery = new BoolQueryBuilder();

    // Match any of the agent's hierarchical tags
    tagQuery.should(QueryBuilders.termsQuery("tags", agentTags));

    // Also handle tasks with no tags (backward compatibility)
    tagQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("tags")));

    return tagQuery;
}
```

#### Matching Scenarios
```java
// Scenario 1: Org-specific, Any Role
// Task tags: ["ORG_MR_LENDING_DIRECT_ANY"]
// Agent tags: ["ORG_MR_LENDING_DIRECT_FIELD_AGENT", "ORG_MR_LENDING_DIRECT_FIELD_AGENT_EXCLUSIVE_LENDING"]
// Result: ✅ Match (agent belongs to the org)

// Scenario 2: Specific Role + Cohort
// Task tags: ["ORG_MR_LENDING_DIRECT_SENIOR_AGENT_EXCLUSIVE_LENDING"]
// Agent tags: ["ORG_MR_LENDING_DIRECT_FIELD_AGENT_EXCLUSIVE_LENDING"]
// Result: ❌ No Match (different role)

// Scenario 3: Cross-Org Cohort
// Task tags: ["ANY_ANY_HIGH_VALUE"]
// Agent tags: ["ORG_MR_LENDING_DIRECT_FIELD_AGENT_HIGH_VALUE"]
// Result: ✅ Match (cohort-based matching across orgs)
```

**Pros:**
- No Legion schema changes required
- Clean query logic - simple ES terms query
- Flexible matching patterns with `ANY` wildcards
- Self-contained hierarchy information in tags
- No Chimera restrictions needed
- Future extensible (easy to add new hierarchy levels)

**Cons:**
- String parsing overhead for tag generation
- Potential tag explosion with many combinations
- Need to migrate existing campaigns and tasks

#### Migration Strategy for Approach 2
```java
// Phase 1: Parallel Tag Generation
public static Set<String> transitionTagEnricher(AgentProfile agentProfile) {
    Set<String> allTags = new HashSet<>();

    // Old tags (for backward compatibility)
    allTags.addAll(legacyTagEnricher(agentProfile));

    // New hierarchical tags
    allTags.addAll(hierarchicalTagEnricher(agentProfile));

    return allTags;
}

// Phase 2: Campaign Migration
// Update existing campaigns to use hierarchical tags
UPDATE gladius.campaigns
SET tags = CONCAT('ORG_', org_id, '_', role_v2, '_', existing_tag)
WHERE hierarchy_version = 'v2';

// Phase 3: ES Document Migration
public void migrateTaskInstanceTags() {
    List<TaskInstance> tasks = getAllTaskInstances();

    for (TaskInstance task : tasks) {
        Campaign campaign = getCampaign(task.getCampaignId());
        if (campaign.getHierarchyVersion().equals("v2")) {
            task.setTags(campaign.getHierarchicalTags());
            esRepository.update(task);
        }
    }
}
```

---

### Recommendation

**Approach 2 (Hierarchical Underscore-Delimited Tags)** is recommended because:

1. **No Legion Changes**: Uses existing `type=TAG` structure
2. **Simple Implementation**: Single ES terms query, no complex boolean logic
3. **Flexible Patterns**: `ANY` wildcards provide powerful matching capabilities
4. **Clean Architecture**: Eliminates need for Chimera restrictions
5. **Future-Proof**: Easy to extend with new hierarchy levels

This approach solves the fundamental OR/AND problem by encoding the hierarchy directly in the tag structure, making the filtering logic much simpler and more maintainable.

---

## Extended Long-Term Solution: TaskDefinition-Level Rules Engine

### Current Problem with Chimera
The current implementation relies on Chimera for complex visibility restrictions:

**Current Flow:**
1. Agent requests tasks → Gladius calls Chimera with agent profile
2. Chimera evaluates multiple condition/restriction pairs
3. Returns list of restricted task definition IDs
4. Gladius excludes these from ES query

**Chimera Config Example:**
```json
// Main config with treatment groups
{
  "treatmentGroup": "default",
  "restrictionsEnabled": true,
  "restrictions": [
    [{
      "conditionKey": "gladius_lending_condition_on_edc_servicing_agents",
      "restrictionKey": "gladius_lending_restriction_on_edc_servicing_agents"
    }]
  ]
}

// Condition config
"gladius_lending_condition_on_edc_servicing_agents": {
  "orConditions": [[{
    "type": "CONTAINS_ANY",
    "field": "agentTags",
    "values": ["EDC_SERVICING"]
  }]]
}

// Restriction config
"gladius_lending_restriction_on_edc_servicing_agents": {
  "orConditions": [[{
    "type": "NOT_IN",
    "field": "task_definition_id",
    "values": ["TD_EDC_ORDER_PLACEMENT_20P_MR_LP", "TD_EDC_ORDER_PLACEMENT_15P_MR", ...]
  }]]
}
```

**Problems:**
- External dependency on Chimera service
- Complex multi-level configuration management
- Difficult to debug and maintain
- No direct relationship between TaskDefinition and its visibility rules

### Proposed Solution: TaskDefinition Rules Engine

#### Enhanced TaskDefinition Model
```java
// Enhanced StoredTaskDefinition
public class StoredTaskDefinition {
    private String definitionId;
    private String actionId;
    private String name;

    // Current hierarchy fields
    private List<String> rolesNotAllowed;
    private List<String> businessUnits;

    // New hierarchy v2 fields
    private HierarchyV2Config hierarchyV2;

    // NEW: Embedded rules engine
    private TaskVisibilityRules visibilityRules;
    private String hierarchyVersion;

    // ... other fields
}
```

#### TaskVisibilityRules Structure
```java
public class TaskVisibilityRules {
    private boolean rulesEnabled;
    private List<VisibilityCondition> inclusionRules;  // Agent MUST match these
    private List<VisibilityCondition> exclusionRules;  // Agent MUST NOT match these
    private String ruleLogic; // "AND" or "OR"
}

public class VisibilityCondition {
    private String conditionType; // "CONTAINS_ANY", "CONTAINS_ALL", "NOT_IN", "EQUALS", etc.
    private String field;         // "agentTags", "orgId", "roleV2", "businessUnit", "sector", etc.
    private List<String> values;  // Values to match against
    private String operator;      // "AND", "OR" for multiple values
}
```

#### TaskDefinition Creation with Rules
```json
{
  "definitionId": "TD_EDC_ORDER_PLACEMENT_20P_MR_LP",
  "actionId": "ACTION_EDC_ORDER_PLACEMENT",
  "name": "EDC Order Placement 20P MR LP",

  // Hierarchy v2 fields
  "hierarchyV2": {
    "orgId": "ORG_MR_LENDING_DIRECT",
    "applicableRoleV2": ["FIELD_AGENT", "SENIOR_AGENT"],
    "businessUnitV2": "MASS_RETAIL",
    "channel": "DIRECT",
    "businessType": "LENDING"
  },

  // NEW: Embedded visibility rules
  "visibilityRules": {
    "rulesEnabled": true,
    "ruleLogic": "AND",
    "inclusionRules": [
      {
        "conditionType": "CONTAINS_ANY",
        "field": "agentTags",
        "values": ["EDC_SERVICING", "EDC_SPECIALIST"],
        "operator": "OR"
      },
      {
        "conditionType": "EQUALS",
        "field": "orgId",
        "values": ["ORG_MR_LENDING_DIRECT"],
        "operator": "AND"
      }
    ],
    "exclusionRules": [
      {
        "conditionType": "CONTAINS_ANY",
        "field": "agentTags",
        "values": ["RESTRICTED_LENDING", "SUSPENDED"],
        "operator": "OR"
      }
    ]
  }
}
```

#### Rules Evaluation Engine
```java
@Service
public class TaskVisibilityRulesEngine {

    public boolean isTaskVisibleToAgent(StoredTaskDefinition taskDef, AgentProfile agent) {
        if (!taskDef.getVisibilityRules().isRulesEnabled()) {
            return true; // No rules = visible to all
        }

        TaskVisibilityRules rules = taskDef.getVisibilityRules();

        // Check inclusion rules (agent MUST match)
        boolean includeMatches = evaluateConditions(rules.getInclusionRules(), agent, rules.getRuleLogic());
        if (!includeMatches) {
            return false;
        }

        // Check exclusion rules (agent MUST NOT match)
        boolean excludeMatches = evaluateConditions(rules.getExclusionRules(), agent, "OR");
        if (excludeMatches) {
            return false;
        }

        return true;
    }

    private boolean evaluateConditions(List<VisibilityCondition> conditions, AgentProfile agent, String logic) {
        if (conditions.isEmpty()) {
            return true;
        }

        List<Boolean> results = conditions.stream()
            .map(condition -> evaluateCondition(condition, agent))
            .collect(Collectors.toList());

        return logic.equals("AND") ?
            results.stream().allMatch(Boolean::booleanValue) :
            results.stream().anyMatch(Boolean::booleanValue);
    }

    private boolean evaluateCondition(VisibilityCondition condition, AgentProfile agent) {
        switch (condition.getConditionType()) {
            case "CONTAINS_ANY":
                return evaluateContainsAny(condition, agent);
            case "CONTAINS_ALL":
                return evaluateContainsAll(condition, agent);
            case "EQUALS":
                return evaluateEquals(condition, agent);
            case "NOT_IN":
                return evaluateNotIn(condition, agent);
            default:
                return false;
        }
    }

    private boolean evaluateContainsAny(VisibilityCondition condition, AgentProfile agent) {
        Set<String> agentValues = getAgentFieldValues(condition.getField(), agent);
        return condition.getValues().stream()
            .anyMatch(agentValues::contains);
    }

    private Set<String> getAgentFieldValues(String field, AgentProfile agent) {
        switch (field) {
            case "agentTags":
                return ProfileUtils.hierarchicalTagEnricher(agent);
            case "orgId":
                return Set.of(agent.getOrgId());
            case "roleV2":
                return Set.of(agent.getRoleV2());
            case "businessUnit":
                return Set.of(agent.getBusinessUnitV2());
            case "sector":
                return agent.getSectors().stream().collect(Collectors.toSet());
            default:
                return Set.of();
        }
    }
}
```

#### Enhanced Task Discovery with Rules Engine
```java
// Updated BaseSearchRequestQueryBuilder
public BoolQueryBuilder getQuery(T request, String actor) {
    AgentProfile userProfile = legionService.getAgentProfile(actor);
    BoolQueryBuilder query = getBaseQuery(states);

    // Get agent's hierarchical tags
    Set<String> agentTags = ProfileUtils.hierarchicalTagEnricher(userProfile);

    // Apply tag-based filtering (campaign level)
    query.must(getHierarchicalTagFilter(agentTags));

    // Apply hierarchy-based filtering
    query.must(getHierarchyFilter(userProfile));

    // NEW: Apply definition-level rules filtering
    query.must(getDefinitionRulesFilter(userProfile));

    return query;
}

private BoolQueryBuilder getDefinitionRulesFilter(AgentProfile userProfile) {
    // Get all cached task definitions with rules
    List<StoredTaskDefinition> allDefinitions = taskDefinitionCache.getAllActiveDefinitions();

    // Filter definitions based on visibility rules
    Set<String> visibleDefinitionIds = allDefinitions.stream()
        .filter(def -> taskVisibilityRulesEngine.isTaskVisibleToAgent(def, userProfile))
        .map(StoredTaskDefinition::getDefinitionId)
        .collect(Collectors.toSet());

    // Include only tasks from visible definitions
    return QueryBuilders.boolQuery()
        .must(QueryBuilders.termsQuery("taskDefinitionId", visibleDefinitionIds));
}
```

#### Caching Strategy for Performance
```java
@Service
public class TaskDefinitionCache {

    @Cacheable(value = "taskDefinitions", key = "'all_active'")
    public List<StoredTaskDefinition> getAllActiveDefinitions() {
        return taskDefinitionRepository.findByActiveTrue();
    }

    @Cacheable(value = "visibleDefinitions", key = "#agentId")
    public Set<String> getVisibleDefinitionIds(String agentId) {
        AgentProfile agent = legionService.getAgentProfile(agentId);
        List<StoredTaskDefinition> allDefinitions = getAllActiveDefinitions();

        return allDefinitions.stream()
            .filter(def -> taskVisibilityRulesEngine.isTaskVisibleToAgent(def, agent))
            .map(StoredTaskDefinition::getDefinitionId)
            .collect(Collectors.toSet());
    }

    @CacheEvict(value = {"taskDefinitions", "visibleDefinitions"}, allEntries = true)
    public void evictCache() {
        // Called when task definitions are updated
    }
}
```

#### API for Managing Definition Rules
```java
// New API endpoint for updating definition rules
@PostMapping("/v1/task/definition/{definitionId}/rules")
public ResponseEntity<TaskDefinitionInstance> updateVisibilityRules(
    @PathVariable String definitionId,
    @RequestBody TaskVisibilityRules rules) {

    TaskDefinitionInstance definition = taskDefinitionService.getById(definitionId);
    definition.setVisibilityRules(rules);

    TaskDefinitionInstance updated = taskDefinitionService.update(definition);

    // Evict cache to refresh visibility rules
    taskDefinitionCache.evictCache();

    return ResponseEntity.ok(updated);
}

// Bulk update API for migrating from Chimera
@PostMapping("/v1/task/definition/rules/bulk-update")
public ResponseEntity<BulkUpdateResponse> bulkUpdateVisibilityRules(
    @RequestBody BulkRulesUpdateRequest request) {

    List<TaskDefinitionRulesUpdate> updates = request.getUpdates();
    List<String> successIds = new ArrayList<>();
    List<String> failedIds = new ArrayList<>();

    for (TaskDefinitionRulesUpdate update : updates) {
        try {
            TaskDefinitionInstance definition = taskDefinitionService.getById(update.getDefinitionId());
            definition.setVisibilityRules(update.getRules());
            taskDefinitionService.update(definition);
            successIds.add(update.getDefinitionId());
        } catch (Exception e) {
            failedIds.add(update.getDefinitionId());
        }
    }

    taskDefinitionCache.evictCache();

    return ResponseEntity.ok(BulkUpdateResponse.builder()
        .successCount(successIds.size())
        .failedCount(failedIds.size())
        .successIds(successIds)
        .failedIds(failedIds)
        .build());
}
```

#### Migration from Chimera to TaskDefinition Rules
```java
@Service
public class ChimeraToRulesMigrationService {

    public void migrateChimeraConfigToDefinitionRules() {
        // Step 1: Get all Chimera restriction configs
        ChimeraMainConfig mainConfig = chimeraService.getMainConfig();

        // Step 2: For each restriction, identify affected task definitions
        for (RestrictionConfig restriction : mainConfig.getRestrictions()) {
            String conditionKey = restriction.getConditionKey();
            String restrictionKey = restriction.getRestrictionKey();

            // Get condition and restriction details
            ChimeraCondition condition = chimeraService.getCondition(conditionKey);
            ChimeraRestriction restrictionConfig = chimeraService.getRestriction(restrictionKey);

            // Step 3: Convert to TaskVisibilityRules
            TaskVisibilityRules rules = convertChimeraToRules(condition, restrictionConfig);

            // Step 4: Apply rules to affected task definitions
            List<String> affectedDefinitionIds = extractDefinitionIds(restrictionConfig);

            for (String definitionId : affectedDefinitionIds) {
                TaskDefinitionInstance definition = taskDefinitionService.getById(definitionId);
                definition.setVisibilityRules(rules);
                taskDefinitionService.update(definition);
            }
        }

        taskDefinitionCache.evictCache();
    }

    private TaskVisibilityRules convertChimeraToRules(ChimeraCondition condition, ChimeraRestriction restriction) {
        // Convert Chimera condition to inclusion rules
        List<VisibilityCondition> inclusionRules = condition.getOrConditions().stream()
            .flatMap(List::stream)
            .map(this::convertChimeraConditionToVisibilityCondition)
            .collect(Collectors.toList());

        // For restrictions, we typically want to EXCLUDE agents matching the condition
        // So inclusion rules become exclusion rules
        return TaskVisibilityRules.builder()
            .rulesEnabled(true)
            .ruleLogic("OR")
            .inclusionRules(new ArrayList<>()) // No specific inclusion rules
            .exclusionRules(inclusionRules)    // Exclude agents matching condition
            .build();
    }

    private VisibilityCondition convertChimeraConditionToVisibilityCondition(ChimeraConditionItem item) {
        return VisibilityCondition.builder()
            .conditionType(item.getType())
            .field(item.getField())
            .values(item.getValues())
            .operator("OR")
            .build();
    }

    private List<String> extractDefinitionIds(ChimeraRestriction restriction) {
        return restriction.getOrConditions().stream()
            .flatMap(List::stream)
            .filter(item -> "task_definition_id".equals(item.getField()))
            .flatMap(item -> item.getValues().stream())
            .collect(Collectors.toList());
    }
}
```

### Benefits of TaskDefinition-Level Rules

#### 1. **Eliminates External Dependencies**
- No more Chimera service calls
- Self-contained visibility logic
- Reduced latency and complexity

#### 2. **Granular Control**
- Each TaskDefinition has its own visibility rules
- Easy to debug and maintain
- Clear relationship between definition and its rules

#### 3. **Performance Optimization**
- Cache 500-700 active definitions in memory
- Single ES query with pre-filtered definition IDs
- No runtime Chimera evaluation

#### 4. **Flexible Rule Engine**
- Support for complex AND/OR logic
- Multiple condition types (CONTAINS_ANY, EQUALS, NOT_IN, etc.)
- Easy to extend with new condition types

#### 5. **Maintainability**
- Rules are stored with the definition
- Version control and audit trail
- Easy bulk updates via API

### Implementation Timeline

#### Phase 1: Rules Engine Development (2 weeks)
- Implement TaskVisibilityRules model
- Build rules evaluation engine
- Add caching layer
- Create management APIs

#### Phase 2: Migration Tooling (1 week)
- Build Chimera to Rules migration service
- Create bulk update APIs
- Implement validation and testing tools

#### Phase 3: Gradual Migration (2 weeks)
- Migrate definitions in batches
- Run parallel evaluation (Chimera + Rules)
- Validate consistency and performance

#### Phase 4: Chimera Removal (1 week)
- Switch to rules-only evaluation
- Remove Chimera dependencies
- Performance optimization and monitoring

### Long-term Vision

This approach provides a foundation for:

1. **Advanced Targeting**: Complex agent targeting based on multiple attributes
2. **Dynamic Rules**: Runtime rule updates without code deployment
3. **A/B Testing**: Different rule sets for different agent groups
4. **Analytics**: Track rule effectiveness and agent task distribution
5. **Self-Service**: Business teams can manage visibility rules directly

**Note**: Campaign tags remain intact with the hierarchical underscore-delimited approach discussed earlier. This rules engine complements the campaign-level filtering by providing definition-level granular control.

This extended solution transforms Gladius from a simple tag-matching system to a sophisticated, rule-based task visibility engine while maintaining backward compatibility and improving performance.
```
