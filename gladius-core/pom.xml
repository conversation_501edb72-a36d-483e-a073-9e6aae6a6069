<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gladius</artifactId>
        <groupId>com.phonepe.merchant</groupId>
        <version>1.0.384-SNAPSHOT</version>
    </parent>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>gladius-core</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>gladius-models</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-reflect</artifactId>
            <version>1.6.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.phonepe.user</groupId>
            <artifactId>userservice-model</artifactId>
            <version>2.0.8</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>legion-location</artifactId>
            <version>1.0.512</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mock-server</groupId>
            <artifactId>mockserver-netty</artifactId>
            <version>5.11.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>ru.vyarus</groupId>
            <artifactId>dropwizard-guicey</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hystrix</groupId>
            <artifactId>hystrix-configurator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.platform</groupId>
            <artifactId>validation-bundle</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.gandalf</groupId>
            <artifactId>gandalf-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>olympus-im-routing</artifactId>
                    <groupId>com.phonepe.olympus-im</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>rosey-dropwizard-config</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>feign-ranger</artifactId>
                    <groupId>feign.ranger</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.raven.dropwizard</groupId>
            <artifactId>dropwizard-oor</artifactId>
            <version>2.0.24-1</version>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>metric-ingestion-bundle</artifactId>
        </dependency>
        <dependency>
            <groupId>org.zapodot</groupId>
            <artifactId>hystrix-dropwizard-bundle</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.dataplatform</groupId>
            <artifactId>event-ingestion-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>ranger</artifactId>
                    <groupId>com.flipkart.ranger</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>feign-ranger</artifactId>
                    <groupId>feign.ranger</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>legion-models</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.models</groupId>
            <artifactId>merchant-service-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.frontend</groupId>
            <artifactId>chimera-lite</artifactId>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.fsm</groupId>
            <artifactId>fsm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.inject</groupId>
            <artifactId>guice</artifactId>
        </dependency>

        <dependency>
            <groupId>io.appform.dropwizard.actors</groupId>
            <artifactId>dropwizard-rabbitmq-actors</artifactId>
        </dependency>
        <dependency>
            <artifactId>clockwork-models</artifactId>
            <groupId>com.phonepe.platform</groupId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>brickbat-models</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>atlas-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchants</groupId>
            <artifactId>odin-ace-models</artifactId>
        </dependency>
        <dependency>
            <groupId>com.flipkart.foxtrot</groupId>
            <artifactId>foxtrot-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.appform.dropwizard.discovery</groupId>
                    <artifactId>dropwizard-service-discovery-bundle</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.phonepe.platform</groupId>
                    <artifactId>event-ingestion-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId> com.phonepe.platform.http.v2</groupId>
                    <artifactId>http-feign</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.appform.dropwizard.sharding</groupId>
            <artifactId>db-sharding-bundle</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchants</groupId>
            <artifactId>paradox-models</artifactId>
        </dependency>
        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>20.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform.killswitch</groupId>
            <artifactId>killswitch-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.appform.hope</groupId>
                    <artifactId>hope-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.phonepe.platform.filters</groupId>
            <artifactId>api-killer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>legion-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.gemini</groupId>
            <artifactId>gemini-model</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>