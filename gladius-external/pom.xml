<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gladius</artifactId>
        <groupId>com.phonepe.merchant</groupId>
        <version>1.0.384-SNAPSHOT</version>
    </parent>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>gladius-external</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>gladius-models</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>gladius-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform.filters</groupId>
            <artifactId>api-killer-core</artifactId>
        </dependency>
    </dependencies>
    
</project>